<?php
/**
 * License Settings Page
 *
 * Provides interface for activating and managing the Envato purchase code.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Process form submission
if (isset($_POST['dab_license_action'])) {
    // Verify nonce
    if (!isset($_POST['dab_license_nonce']) || !wp_verify_nonce($_POST['dab_license_nonce'], 'dab_license_action')) {
        echo '<div class="notice notice-error is-dismissible"><p>' . __('Security check failed', 'db-app-builder') . '</p></div>';
    } else {
        $license_action = sanitize_text_field($_POST['dab_license_action']);

        if ($license_action === 'activate' && isset($_POST['dab_purchase_code'])) {
            $purchase_code = sanitize_text_field($_POST['dab_purchase_code']);

            // Enable debug mode for this activation attempt if requested
            if (isset($_POST['dab_enable_debug']) && $_POST['dab_enable_debug'] == '1') {
                update_option('dab_debug_mode', true);
            }

            $response = DAB_License()->activate_license($purchase_code);

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                $error_code = $response->get_error_code();
                $error_data = $response->get_error_data();

                // Display detailed error message
                echo '<div class="notice notice-error is-dismissible">';
                echo '<p><strong>' . esc_html($error_message) . '</strong></p>';

                // Debug information hidden for production use

                echo '</div>';
            } else {
                if (isset($response['success']) && $response['success'] === true) {
                    echo '<div class="notice notice-success is-dismissible"><p>' . __('Purchase code activated successfully!', 'db-app-builder') . '</p>';
                    if (isset($response['domain'])) {
                        echo '<p>' . sprintf(__('Activated on domain: %s', 'db-app-builder'), '<strong>' . esc_html($response['domain']) . '</strong>') . '</p>';
                    }
                    echo '</div>';
                } else {
                    $message = isset($response['message']) ? $response['message'] : __('Purchase code activation failed.', 'db-app-builder');
                    echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($message) . '</p></div>';
                }
            }
        } elseif ($license_action === 'deactivate') {
            $response = DAB_License()->deactivate_license();

            if (is_wp_error($response)) {
                echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($response->get_error_message()) . '</p></div>';
            } else {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Purchase code deactivated successfully!', 'db-app-builder') . '</p></div>';
            }
        } elseif ($license_action === 'check') {
            $response = DAB_License()->check_license();

            if (is_wp_error($response)) {
                echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($response->get_error_message()) . '</p></div>';
            } else {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Purchase code status checked successfully!', 'db-app-builder') . '</p></div>';
            }
        } elseif ($license_action === 'toggle_debug') {
            $debug_mode = isset($_POST['dab_debug_mode']) && $_POST['dab_debug_mode'] == '1';
            update_option('dab_debug_mode', $debug_mode);

            if ($debug_mode) {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Debug mode enabled. Detailed error information will be displayed and logged.', 'db-app-builder') . '</p></div>';
            } else {
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Debug mode disabled.', 'db-app-builder') . '</p></div>';
            }
        } elseif ($license_action === 'clear_debug_log') {
            if (function_exists('DAB_Debug')) {
                DAB_Debug()->clear_debug_log();
                echo '<div class="notice notice-success is-dismissible"><p>' . __('Debug log cleared successfully.', 'db-app-builder') . '</p></div>';
            }
        }
    }
}

// Get license data
$license_data = DAB_License()->get_license_data();
$purchase_code = $license_data['purchase_code'];
$license_status = $license_data['status'];
$domain = $license_data['domain'];
$activated_at = $license_data['activated_at'];
$item_name = $license_data['item_name'];
$buyer = $license_data['buyer'];

// Get current domain
$home_url = home_url();
if (function_exists('dab_safe_str_replace')) {
    $current_domain = dab_safe_str_replace(array('http://', 'https://'), '', $home_url);
} else {
    $current_domain = str_replace(array('http://', 'https://'), '', ($home_url ?? ''));
}

// Format activation date
$activation_formatted = '';
if (!empty($activated_at)) {
    $activation_time = strtotime($activated_at);
    if ($activation_time) {
        $activation_formatted = date_i18n(get_option('date_format'), $activation_time);
    }
}

// Get status text and class
$status_text = '';
$status_class = '';

switch ($license_status) {
    case 'valid':
        if ($domain === $current_domain) {
            $status_text = __('Active', 'db-app-builder');
            $status_class = 'dab-license-active';
        } else {
            $status_text = __('Domain Mismatch', 'db-app-builder');
            $status_class = 'dab-license-invalid';
        }
        break;
    case 'invalid':
        $status_text = __('Invalid', 'db-app-builder');
        $status_class = 'dab-license-invalid';
        break;
    default:
        $status_text = __('Inactive', 'db-app-builder');
        $status_class = 'dab-license-inactive';
        break;
}
?>

<div class="wrap dab-admin-wrap">
    <h1><?php _e('License Settings', 'db-app-builder'); ?></h1>
    <p class="description"><?php _e('Manage your Database App Builder Envato purchase code to receive updates and support.', 'db-app-builder'); ?></p>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title"><?php _e('License Information', 'db-app-builder'); ?></h2>
        </div>
        <div class="dab-card-body">
            <?php if (empty($purchase_code)) : ?>
                <p><?php _e('Enter your Envato purchase code to activate Database App Builder.', 'db-app-builder'); ?></p>

                <form method="post" action="">
                    <?php wp_nonce_field('dab_license_action', 'dab_license_nonce'); ?>
                    <input type="hidden" name="dab_license_action" value="activate">

                    <table class="form-table">
                        <tr valign="top">
                            <th scope="row"><?php _e('Purchase Code', 'db-app-builder'); ?></th>
                            <td>
                                <input type="text" name="dab_purchase_code" class="regular-text" placeholder="<?php esc_attr_e('Enter your Envato purchase code', 'db-app-builder'); ?>">
                                <p class="description"><?php _e('Your purchase code is a 36-character code provided by Envato when you purchased the plugin.', 'db-app-builder'); ?></p>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <input type="submit" class="button button-primary" value="<?php esc_attr_e('Activate Purchase Code', 'db-app-builder'); ?>">
                    </p>
                </form>
            <?php else : ?>
                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php _e('Purchase Code', 'db-app-builder'); ?></th>
                        <td>
                            <code><?php echo esc_html(DAB_License()->mask_purchase_code($purchase_code)); ?></code>
                        </td>
                    </tr>
                    <tr valign="top">
                        <th scope="row"><?php _e('Status', 'db-app-builder'); ?></th>
                        <td>
                            <span class="dab-license-status <?php echo esc_attr($status_class); ?>"><?php echo esc_html($status_text); ?></span>
                        </td>
                    </tr>
                    <tr valign="top">
                        <th scope="row"><?php _e('Domain', 'db-app-builder'); ?></th>
                        <td>
                            <?php echo esc_html($domain); ?>
                            <?php if ($domain !== $current_domain) : ?>
                                <span class="dab-license-warning"><?php _e('(Domain mismatch with current site)', 'db-app-builder'); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if (!empty($activation_formatted)) : ?>
                        <tr valign="top">
                            <th scope="row"><?php _e('Activated On', 'db-app-builder'); ?></th>
                            <td>
                                <?php echo esc_html($activation_formatted); ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                    <?php if (!empty($item_name)) : ?>
                        <tr valign="top">
                            <th scope="row"><?php _e('Product', 'db-app-builder'); ?></th>
                            <td>
                                <?php echo esc_html($item_name); ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                    <?php if (!empty($buyer)) : ?>
                        <tr valign="top">
                            <th scope="row"><?php _e('Buyer', 'db-app-builder'); ?></th>
                            <td>
                                <?php echo esc_html($buyer); ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </table>

                <form method="post" action="" class="dab-license-actions">
                    <?php wp_nonce_field('dab_license_action', 'dab_license_nonce'); ?>

                    <p class="submit">
                        <button type="submit" name="dab_license_action" value="check" class="button button-secondary">
                            <?php _e('Check License Status', 'db-app-builder'); ?>
                        </button>

                        <button type="submit" name="dab_license_action" value="deactivate" class="button button-secondary">
                            <?php _e('Deactivate License', 'db-app-builder'); ?>
                        </button>
                    </p>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <div class="dab-card">
        <div class="dab-card-header">
            <h2 class="dab-card-title"><?php _e('License FAQ', 'db-app-builder'); ?></h2>
        </div>
        <div class="dab-card-body">
            <h3><?php _e('Where can I find my Envato purchase code?', 'db-app-builder'); ?></h3>
            <p><?php _e('Your purchase code is included in your purchase receipt email from Envato. You can also find it in your Envato account under Downloads > View Purchase Code.', 'db-app-builder'); ?></p>

            <h3><?php _e('How many sites can I use my purchase code on?', 'db-app-builder'); ?></h3>
            <p><?php _e('According to Envato\'s standard license, each purchase code can only be used on one domain at a time. If you want to use the plugin on another site, you need to deactivate it from the current site first or purchase another license.', 'db-app-builder'); ?></p>

            <h3><?php _e('What happens if I change my domain?', 'db-app-builder'); ?></h3>
            <p><?php _e('If you move your site to a new domain, you\'ll need to deactivate the purchase code from the old domain first, then activate it on the new domain.', 'db-app-builder'); ?></p>

            <h3><?php _e('How do I get support?', 'db-app-builder'); ?></h3>
            <p><?php _e('Support is available to customers with a valid purchase code. Please make sure your purchase code is activated before requesting support.', 'db-app-builder'); ?></p>
        </div>
    </div>
</div>

<style>
.dab-license-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-weight: bold;
}
.dab-license-active {
    background-color: #dff0d8;
    color: #3c763d;
}
.dab-license-inactive,
.dab-license-invalid {
    background-color: #f2dede;
    color: #a94442;
}
.dab-license-warning {
    color: #a94442;
    font-weight: bold;
    margin-left: 5px;
}
.dab-license-actions .button {
    margin-right: 10px;
}
input[name="dab_purchase_code"] {
    font-family: monospace;
    letter-spacing: 1px;
}
</style>
